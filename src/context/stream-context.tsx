import React, { createContext, useContext, useState, ReactNode } from "react";

export interface LiveStreamData {
	id: string;
	title: string;
	description: string;
	youtubeUrl: string;
	thumbnail: string;
	viewers: string;
	status: "live" | "upcoming" | "ended";
}

// Initial mock streams (same as existing Home page mock)
const initialStreams: LiveStreamData[] = [
	{
		id: "1",
		title: "Gaming Tournament Live",
		description:
			"Experience the thrill of live gaming tournaments with top players and exciting matches.",
		youtubeUrl: "https://www.youtube.com/watch?v=6Sz2bGhqt2s",
		thumbnail: "https://img.youtube.com/vi/6Sz2bGhqt2s/0.jpg",
		viewers: "22.1K",
		status: "live",
	},
	{
		id: "2",
		title: "Sui Overflow 2025 Demo Day Session One",
		description:
			"Session One of Sui Overflow 2025 Hackathon Demo Day\n\nSui Overflow 2025 is the second edition of Sui's global virtual hackathon, uniting builders and developers worldwide to redefine innovation, performance, and ownership. With over $500,000 USD in prizes across 9 project tracks, take this opportunity to shape the future on Web3's most powerful and composable platform.",
		youtubeUrl: "https://www.youtube.com/watch?v=wzqaCwe37E0",
		thumbnail: "https://img.youtube.com/vi/wzqaCwe37E0/0.jpg",
		viewers: "12.5K",
		status: "live",
	},
	{
		id: "3",
		title: "Market Predictions Hub",
		description:
			"Join our prediction market experts as they analyze trends and share insights on upcoming events.",
		youtubeUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
		thumbnail: "https://img.youtube.com/vi/dQw4w9WgXcQ/0.jpg",
		viewers: "15.7K",
		status: "live",
	},
	{
		id: "4",
		title: "BTC Price Prediction Live",
		description:
			"Watch live Bitcoin price analysis and predictions with real-time market insights and trading opportunities.",
		youtubeUrl: "https://www.youtube.com/watch?v=PVJ1d7mg3j8",
		thumbnail: "https://img.youtube.com/vi/PVJ1d7mg3j8/0.jpg",
		viewers: "12.5K",
		status: "live",
	},
	{
		id: "5",
		title: "OceanDAO",
		description:
			" a 10-day post-hackathon builder retreat bringing together the top developers from the Sui Overflow 2025 hackathon. Hosted at the vibrant SuiHub Athens, this inaugural gathering is designed to turn short-term momentum into long-term impact by supporting the most promising projects and teams in the ecosystem.",
		youtubeUrl: "https://www.youtube.com/watch?v=wzqaCwe37E0",
		thumbnail: "src/assets/imgs/live/oceanDAO.png",
		viewers: "8.2K",
		status: "live",
	},
];

interface StreamContextValue {
	streams: LiveStreamData[];
	addStream: (stream: LiveStreamData) => void;
}

const StreamContext = createContext<StreamContextValue | undefined>(undefined);

export const StreamProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
	const [streams, setStreams] = useState<LiveStreamData[]>(initialStreams);

	const addStream = (stream: LiveStreamData) => {
		setStreams(prev => [stream, ...prev]);
	};

	return <StreamContext.Provider value={{ streams, addStream }}>{children}</StreamContext.Provider>;
};

export const useStreams = () => {
	const ctx = useContext(StreamContext);
	if (!ctx) {
		throw new Error("useStreams must be used within StreamProvider");
	}
	return ctx;
};
