// Elegant Skeleton Loading Components
import React from "react";

// Base skeleton component with shimmer effect
const SkeletonBase: React.FC<{ className?: string; children?: React.ReactNode }> = ({
	className = "",
	children
}) => (
	<div className={`animate-pulse bg-gradient-to-r from-slate-100 via-slate-50 to-slate-100 bg-[length:200%_100%] animate-shimmer rounded-lg ${className}`}>
		{children}
	</div>
);

// Skeleton for prediction market cards
export const PredictionMarketSkeleton: React.FC = () => (
	<div className="py-4 trove-scrollbar flex gap-4">
		{[1, 2, 3].map((index) => (
			<div key={index} className="min-w-[300px] max-w-[300px] p-4 border-2 border-slate-100 rounded-xl shadow-sm">
				<div className="flex justify-between items-center mb-4">
					<SkeletonBase className="h-6 w-32" />
					<SkeletonBase className="h-8 w-16 rounded-md" />
				</div>
				<div className="flex gap-2">
					<SkeletonBase className="flex-1 h-12 rounded-md" />
					<SkeletonBase className="flex-1 h-12 rounded-md" />
				</div>
			</div>
		))}
	</div>
);

// Skeleton for chat messages
export const ChatSkeleton: React.FC = () => (
	<div className="rounded-lg border border-gray-200 bg-white flex flex-col h-[320px]">
		{/* Header */}
		<div className="px-3 py-2 border-b border-gray-200 bg-gray-50">
			<SkeletonBase className="h-4 w-20" />
		</div>

		{/* Messages */}
		<div className="flex-1 overflow-y-auto px-3 py-2 space-y-3 max-h-[200px]">
			{[1, 2, 3, 4].map((index) => (
				<div key={index} className="flex items-start gap-2">
					<SkeletonBase className="h-4 w-16" />
					<SkeletonBase className="h-4 flex-1" />
					<SkeletonBase className="h-3 w-8" />
				</div>
			))}
		</div>

		{/* Input box */}
		<div className="px-3 py-2 border-t border-gray-200 bg-gray-50">
			<div className="flex items-center gap-2">
				<SkeletonBase className="flex-1 h-8 rounded" />
				<SkeletonBase className="h-8 w-16 rounded" />
			</div>
		</div>
	</div>
);

// Skeleton for forum/comments
export const ForumSkeleton: React.FC = () => (
	<div className="border border-gray-200 rounded-lg bg-white p-4">
		<div className="flex items-center gap-2 mb-3">
			<SkeletonBase className="h-5 w-20" />
			<SkeletonBase className="h-4 w-8" />
		</div>

		{/* Input area */}
		<div className="flex items-start gap-2 mb-4">
			<SkeletonBase className="flex-1 h-16 rounded-lg" />
			<SkeletonBase className="h-10 w-16 rounded-lg" />
		</div>

		{/* Comments */}
		<div className="flex flex-col gap-4">
			{[1, 2, 3].map((index) => (
				<div key={index} className="flex gap-3">
					<SkeletonBase className="w-8 h-8 rounded-full flex-shrink-0" />
					<div className="flex-1">
						<div className="flex items-center gap-2 mb-2">
							<SkeletonBase className="h-4 w-20" />
							<SkeletonBase className="h-4 w-12" />
							<SkeletonBase className="h-3 w-16 ml-auto" />
						</div>
						<SkeletonBase className="h-4 w-full mb-1" />
						<SkeletonBase className="h-4 w-3/4" />
					</div>
				</div>
			))}
		</div>
	</div>
);

// Skeleton for video player
export const VideoPlayerSkeleton: React.FC = () => (
	<div className="w-full border-2 border-slate-200 rounded-xl overflow-hidden relative">
		<div className="aspect-video bg-gradient-to-br from-slate-100 via-slate-50 to-slate-100 flex items-center justify-center">
			<div className="text-center">
				<div className="w-16 h-16 border-4 border-slate-300 border-t-slate-500 rounded-full animate-spin mb-4"></div>
				<p className="text-slate-500 text-sm font-medium">Loading video...</p>
			</div>
		</div>

		{/* Fake controls overlay */}
		<div className="absolute bottom-4 left-4 right-4">
			<div className="flex items-center gap-3">
				<SkeletonBase className="w-8 h-8 rounded-full" />
				<SkeletonBase className="flex-1 h-2 rounded-full" />
				<SkeletonBase className="w-12 h-6 rounded" />
			</div>
		</div>
	</div>
);

// Gentle loading state with breathing animation
export const GentleLoader: React.FC<{ message?: string }> = ({
	message = "Loading..."
}) => (
	<div className="py-16 px-8 text-center">
		<div className="max-w-sm mx-auto">
			<div className="relative mb-6">
				<div className="w-12 h-12 border-4 border-slate-200 border-t-slate-400 rounded-full animate-spin mx-auto"></div>
				<div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-t-slate-300 rounded-full animate-ping mx-auto opacity-20"></div>
			</div>
			<p className="text-slate-500 text-sm font-medium animate-pulse">{message}</p>
		</div>
	</div>
);

// Shimmer animation for skeleton components
export const shimmerKeyframes = `
@keyframes shimmer {
	0% {
		background-position: -200% 0;
	}
	100% {
		background-position: 200% 0;
	}
}

@keyframes fade-in {
	0% {
		opacity: 0;
		transform: translateY(10px);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

.animate-shimmer {
	animation: shimmer 2s infinite linear;
}

.animate-fade-in {
	animation: fade-in 0.6s ease-out forwards;
	opacity: 0;
}
`;

// Component to inject shimmer styles
export const ShimmerStyles: React.FC = () => (
	<style jsx global>{shimmerKeyframes}</style>
);
