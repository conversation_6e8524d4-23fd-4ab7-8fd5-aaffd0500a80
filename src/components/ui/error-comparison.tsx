// Visual comparison component to demonstrate UI improvements
import React from "react";

// Old harsh error styles (for comparison)
const OldErrorBanner = () => (
	<div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
		<div className="flex justify-between items-start">
			<div>
				<h3 className="text-yellow-800 font-medium">Game Not Found</h3>
				<p className="text-yellow-700 text-sm mt-1">No prediction markets found for game ID: 999</p>
			</div>
			<button className="text-yellow-600 hover:text-yellow-800 ml-4" title="Dismiss">
				×
			</button>
		</div>
	</div>
);

const OldErrorState = () => (
	<div className="py-4 px-4 bg-red-50 border border-red-200 rounded-lg">
		<h3 className="text-red-800 font-medium mb-2">Unable to Load Prediction Markets</h3>
		<p className="text-red-600 text-sm mb-3">No prediction markets found for game ID: 999</p>
		<button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
			Retry
		</button>
	</div>
);

const OldEmptyState = () => (
	<div className="py-8 px-4 text-center bg-gray-50 border border-gray-200 rounded-lg">
		<h3 className="text-gray-700 font-medium mb-2">No Prediction Markets Available</h3>
		<p className="text-gray-500 text-sm mb-4">No markets found for game ID: 999</p>
		<button className="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md text-sm font-medium">
			Refresh
		</button>
	</div>
);

// New elegant styles
import {
	GentleNotification,
	PredictionMarketsEmptyState,
	ContractErrorState,
} from "./elegant-error-states";

export const ErrorComparisonDemo: React.FC = () => {
	return (
		<div className="p-8 space-y-12 bg-gray-100 min-h-screen">
			<div className="max-w-4xl mx-auto">
				<h1 className="text-3xl font-bold text-center mb-8">Error Handling UI Improvements</h1>

				{/* Notification Comparison */}
				<div className="bg-white rounded-2xl p-6 shadow-lg mb-8">
					<h2 className="text-xl font-semibold mb-6">Notification Banner</h2>
					<div className="grid md:grid-cols-2 gap-6">
						<div>
							<h3 className="text-sm font-medium text-gray-600 mb-3">❌ Before (Harsh)</h3>
							<OldErrorBanner />
						</div>
						<div>
							<h3 className="text-sm font-medium text-gray-600 mb-3">✅ After (Gentle)</h3>
							<GentleNotification
								title="Game Not Found"
								message="No prediction markets found for game ID: 999"
								type="info"
							/>
						</div>
					</div>
				</div>

				{/* Error State Comparison */}
				<div className="bg-white rounded-2xl p-6 shadow-lg mb-8">
					<h2 className="text-xl font-semibold mb-6">Error States</h2>
					<div className="grid md:grid-cols-2 gap-6">
						<div>
							<h3 className="text-sm font-medium text-gray-600 mb-3">❌ Before (Alarming)</h3>
							<OldErrorState />
						</div>
						<div>
							<h3 className="text-sm font-medium text-gray-600 mb-3">✅ After (Sophisticated)</h3>
							<ContractErrorState
								title="Connection Issue"
								message="No prediction markets found for game ID: 999"
								onRetry={() => console.log("Retry clicked")}
							/>
						</div>
					</div>
				</div>

				{/* Empty State Comparison */}
				<div className="bg-white rounded-2xl p-6 shadow-lg mb-8">
					<h2 className="text-xl font-semibold mb-6">Empty States</h2>
					<div className="grid md:grid-cols-2 gap-6">
						<div>
							<h3 className="text-sm font-medium text-gray-600 mb-3">❌ Before (Plain)</h3>
							<OldEmptyState />
						</div>
						<div>
							<h3 className="text-sm font-medium text-gray-600 mb-3">✅ After (Elegant)</h3>
							<PredictionMarketsEmptyState
								gameId="999"
								onRetry={() => console.log("Retry clicked")}
							/>
						</div>
					</div>
				</div>

				{/* Key Improvements */}
				<div className="bg-white rounded-2xl p-6 shadow-lg">
					<h2 className="text-xl font-semibold mb-6">Key Improvements</h2>
					<div className="grid md:grid-cols-2 gap-6">
						<div>
							<h3 className="font-medium text-red-600 mb-3">❌ Old Approach</h3>
							<ul className="space-y-2 text-sm text-gray-600">
								<li>• Harsh red and yellow colors</li>
								<li>• Alarming visual treatment</li>
								<li>• Technical error messages</li>
								<li>• Jarring state transitions</li>
								<li>• Inconsistent styling</li>
								<li>• Poor visual hierarchy</li>
							</ul>
						</div>
						<div>
							<h3 className="font-medium text-green-600 mb-3">✅ New Approach</h3>
							<ul className="space-y-2 text-sm text-gray-600">
								<li>• Soft, professional color palette</li>
								<li>• Gentle, reassuring design</li>
								<li>• User-friendly messaging</li>
								<li>• Smooth animations and transitions</li>
								<li>• Consistent design language</li>
								<li>• Clear visual hierarchy</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};
