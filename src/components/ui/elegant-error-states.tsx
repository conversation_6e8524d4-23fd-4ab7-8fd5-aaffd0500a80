// Elegant Error State Components
import React from "react";

// Icons for different states
const InfoIcon = () => (
	<svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
		<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
	</svg>
);

const SearchIcon = () => (
	<svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
		<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
	</svg>
);

const RefreshIcon = () => (
	<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
		<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
	</svg>
);

const TrendingIcon = () => (
	<svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
		<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
	</svg>
);

const ChatIcon = () => (
	<svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
		<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
	</svg>
);

const VideoIcon = () => (
	<svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
		<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
	</svg>
);

// Gentle notification banner for game not found
interface GentleNotificationProps {
	title?: string;
	message?: string;
	onDismiss?: () => void;
	type?: "info" | "warning";
}

export const GentleNotification: React.FC<GentleNotificationProps> = ({
	title = "Game Not Found",
	message = "The requested game could not be loaded. Showing default content.",
	onDismiss,
	type = "info"
}) => {
	const bgColor = type === "warning" ? "bg-amber-50" : "bg-blue-50";
	const borderColor = type === "warning" ? "border-amber-100" : "border-blue-100";
	const textColor = type === "warning" ? "text-amber-800" : "text-blue-800";
	const subtextColor = type === "warning" ? "text-amber-600" : "text-blue-600";

	return (
		<div className={`mb-6 p-4 ${bgColor} ${borderColor} border rounded-2xl shadow-sm transition-all duration-300 ease-in-out`}>
			<div className="flex items-start gap-3">
				<div className={`${textColor} mt-0.5`}>
					<InfoIcon />
				</div>
				<div className="flex-1">
					<h3 className={`${textColor} font-medium text-sm`}>{title}</h3>
					<p className={`${subtextColor} text-sm mt-1 leading-relaxed`}>{message}</p>
				</div>
				{onDismiss && (
					<button
						onClick={onDismiss}
						className={`${subtextColor} hover:${textColor} transition-colors duration-200 p-1 rounded-full hover:bg-white/50`}
						title="Dismiss"
					>
						<svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
						</svg>
					</button>
				)}
			</div>
		</div>
	);
};

// Elegant empty state for prediction markets
interface EmptyStateProps {
	title?: string;
	description?: string;
	onRetry?: () => void;
	isLoading?: boolean;
	gameId?: string;
}

export const PredictionMarketsEmptyState: React.FC<EmptyStateProps> = ({
	title,
	description,
	onRetry,
	isLoading = false,
	gameId
}) => {
	const defaultTitle = gameId ? "No Markets for This Game" : "No Prediction Markets";
	const defaultDescription = gameId 
		? `We couldn't find any prediction markets for game "${gameId}". Try exploring other games or check back later.`
		: "No prediction markets are currently available. New markets are added regularly.";

	return (
		<div className="py-16 px-8 text-center">
			<div className="max-w-md mx-auto">
				<div className="text-slate-300 mb-6 flex justify-center">
					<TrendingIcon />
				</div>
				<h3 className="text-slate-700 font-medium text-lg mb-3">
					{title || defaultTitle}
				</h3>
				<p className="text-slate-500 text-sm leading-relaxed mb-8">
					{description || defaultDescription}
				</p>
				{onRetry && (
					<button
						onClick={onRetry}
						disabled={isLoading}
						className="inline-flex items-center gap-2 bg-slate-100 hover:bg-slate-200 text-slate-700 px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow-md"
					>
						{isLoading ? (
							<>
								<div className="w-4 h-4 border-2 border-slate-400 border-t-transparent rounded-full animate-spin" />
								Checking...
							</>
						) : (
							<>
								<RefreshIcon />
								Try Again
							</>
						)}
					</button>
				)}
			</div>
		</div>
	);
};

// Elegant error state for contract failures
interface ContractErrorProps {
	title?: string;
	message?: string;
	onRetry?: () => void;
	isLoading?: boolean;
}

export const ContractErrorState: React.FC<ContractErrorProps> = ({
	title = "Connection Issue",
	message = "We're having trouble connecting to the prediction markets. This is usually temporary.",
	onRetry,
	isLoading = false
}) => {
	return (
		<div className="py-12 px-6 text-center bg-slate-50/50 rounded-2xl border border-slate-100">
			<div className="max-w-sm mx-auto">
				<div className="text-slate-300 mb-4 flex justify-center">
					<SearchIcon />
				</div>
				<h3 className="text-slate-700 font-medium mb-2">{title}</h3>
				<p className="text-slate-500 text-sm leading-relaxed mb-6">{message}</p>
				{onRetry && (
					<button
						onClick={onRetry}
						disabled={isLoading}
						className="inline-flex items-center gap-2 bg-slate-600 hover:bg-slate-700 text-white px-5 py-2.5 rounded-xl text-sm font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow-md"
					>
						{isLoading ? (
							<>
								<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
								Reconnecting...
							</>
						) : (
							<>
								<RefreshIcon />
								Retry
							</>
						)}
					</button>
				)}
			</div>
		</div>
	);
};

// Elegant fallback components for different sections
export const VideoPlayerFallback: React.FC = () => (
	<div className="aspect-video bg-gradient-to-br from-slate-50 to-slate-100 rounded-2xl border border-slate-200 flex items-center justify-center">
		<div className="text-center">
			<div className="text-slate-300 mb-3 flex justify-center">
				<VideoIcon />
			</div>
			<p className="text-slate-500 text-sm font-medium">Video temporarily unavailable</p>
			<p className="text-slate-400 text-xs mt-1">Please refresh the page</p>
		</div>
	</div>
);

export const ChatFallback: React.FC = () => (
	<div className="h-[320px] bg-gradient-to-br from-slate-50 to-slate-100 rounded-2xl border border-slate-200 flex items-center justify-center">
		<div className="text-center">
			<div className="text-slate-300 mb-3 flex justify-center">
				<ChatIcon />
			</div>
			<p className="text-slate-500 text-sm font-medium">Chat temporarily unavailable</p>
			<p className="text-slate-400 text-xs mt-1">Please refresh the page</p>
		</div>
	</div>
);

export const GenericFallback: React.FC<{ title: string; height?: string }> = ({ title, height = "auto" }) => (
	<div className={`${height !== "auto" ? height : "py-12"} bg-gradient-to-br from-slate-50 to-slate-100 rounded-2xl border border-slate-200 flex items-center justify-center`}>
		<div className="text-center">
			<div className="text-slate-300 mb-3 flex justify-center">
				<InfoIcon />
			</div>
			<p className="text-slate-500 text-sm font-medium">{title}</p>
			<p className="text-slate-400 text-xs mt-1">Please refresh the page</p>
		</div>
	</div>
);
