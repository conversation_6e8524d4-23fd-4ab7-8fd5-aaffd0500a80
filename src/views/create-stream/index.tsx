// <!-- create-stream -->
import React, { useState } from "react";
import ReactPlayer from "react-player";
import { useNavigate } from "react-router-dom";
import { useStreams, LiveStreamData } from "@/context/stream-context";

const oracleOptions = [
	{ label: "Pyth", value: "pyth" },
	{ label: "Switchboard", value: "switchboard" },
	{ label: "Mysten Simple Oracle", value: "mysten" },
];

// Robust YouTube ID extractor (handles watch?v=, youtu.be/, embed, shorts, etc.)
const extractYoutubeId = (url: string): string | null => {
	const regex =
		/(?:youtube\.com\/(?:[^/\n\s]+\/\S+\/|(?:v|e(?:mbed)?|shorts)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
	const match = url.match(regex);
	return match ? match[1] : null;
};

const View: React.FC = () => {
	const navigate = useNavigate();
	const { addStream } = useStreams();

	const [youtubeLink, setYoutubeLink] = useState<string>("");
	const [name, setName] = useState<string>("");
	const [description, setDescription] = useState<string>("");
	const [oracle, setOracle] = useState<string>("");
	const [oracleCode, setOracleCode] = useState<string>("");

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();

		// Determine thumbnail (if valid id) and random viewers (200 - 5000)
		const id = extractYoutubeId(youtubeLink.trim());
		const thumbnail = id ? `https://img.youtube.com/vi/${id}/0.jpg` : "";
		const randomCount = Math.floor(Math.random() * (5000 - 200 + 1)) + 200;
		const viewersFormatted =
			randomCount >= 1000 ? `${(randomCount / 1000).toFixed(1)}K` : `${randomCount}`;

		// Build new stream object
		const newStream: LiveStreamData = {
			id: Date.now().toString(),
			title: name.trim(),
			description: description.trim(),
			youtubeUrl: youtubeLink.trim(),
			thumbnail,
			viewers: viewersFormatted,
			status: "live",
		};

		addStream(newStream);
		// redirect to home
		navigate("/home");
	};

	return (
		<section className="main py-10 flex flex-col items-center">
			<div className="w-full max-w-2xl bg-white border border-gray-200 rounded-lg shadow-md p-6">
				<h1 className="text-2xl font-bold mb-6 text-gray-900">Create New Stream</h1>
				<form className="space-y-5" onSubmit={handleSubmit}>
					{/* Live Link */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="youtube-link">
							Live Link
						</label>
						<input
							id="youtube-link"
							type="url"
							required
							className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder="https://www.youtube.com/watch?v=..."
							value={youtubeLink}
							onChange={e => setYoutubeLink(e.target.value)}
						/>
						{/* Live preview */}
						{youtubeLink && (
							<div className="mt-4 aspect-video">
								<ReactPlayer url={youtubeLink} width="100%" height="100%" controls />
							</div>
						)}
					</div>

					{/* Name */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="stream-name">
							Name
						</label>
						<input
							id="stream-name"
							type="text"
							required
							className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder="Enter stream name"
							value={name}
							onChange={e => setName(e.target.value)}
						/>
					</div>

					{/* Description */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="description">
							Description
						</label>
						<textarea
							id="description"
							required
							rows={3}
							className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder="Enter description"
							value={description}
							onChange={e => setDescription(e.target.value)}
						/>
					</div>

					{/* Oracle Selection */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="oracle-select">
							Oracle Provider
						</label>
						<select
							id="oracle-select"
							required
							className="w-full border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
							value={oracle}
							onChange={e => {
								setOracle(e.target.value);
								setOracleCode("");
							}}
						>
							<option value="" disabled>
								Select Oracle
							</option>
							{oracleOptions.map(opt => (
								<option key={opt.value} value={opt.value}>
									{opt.label}
								</option>
							))}
						</select>
					</div>

					{/* Oracle Code (only if oracle selected) */}
					{oracle && (
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="oracle-code">
								Oracle Unique Code
							</label>
							<input
								id="oracle-code"
								type="text"
								required
								className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
								placeholder="Enter oracle code"
								value={oracleCode}
								onChange={e => setOracleCode(e.target.value)}
							/>
						</div>
					)}

					{/* Buttons */}
					<div className="flex justify-end gap-3">
						<button
							type="button"
							onClick={() => navigate(-1)}
							className="px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50"
						>
							Cancel
						</button>
						<button
							type="submit"
							className="px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50"
							disabled={
								!youtubeLink.trim() ||
								!name.trim() ||
								!description.trim() ||
								!oracle ||
								!oracleCode.trim()
							}
						>
							Confirm
						</button>
					</div>
				</form>
			</div>
		</section>
	);
};

export default View;
