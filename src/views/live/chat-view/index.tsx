// <!-- chat-view -->
import React, { useState, useEffect } from "react";
import { useChatMessages } from "../chat-context";

interface ChatMessage {
	id: string;
	user: string;
	text: string;
	time: string;
}

interface Props {
	gameId?: string;
}

// Mock chat messages keyed by live stream id
const mockChats: Record<string, ChatMessage[]> = {
	"1": [
		{ id: "m1", user: "GGWP", text: "That clutch play was insane!", time: "16:44" },
		{ id: "m2", user: "NoScope", text: "MVP right there", time: "16:45" },
		{ id: "m3", user: "ProStrat", text: "Next round eco?", time: "16:46" },
	],
	"2": [
		{ id: "m1", user: "HackBuilder", text: "Amazing project showcase!", time: "16:00" },
		{ id: "m2", user: "PanelHost", text: "Judges seem impressed.", time: "16:01" },
		{ id: "m3", user: "ViewerX", text: "Can’t wait to try this on <PERSON>i.", time: "16:02" },
	],
	"3": [
		{ id: "m1", user: "MarketGuru", text: "New poll results just dropped", time: "18:05" },
		{ id: "m2", user: "TrendSpot", text: "Odds shifting fast!", time: "18:06" },
		{ id: "m3", user: "AlphaWolf", text: "Loading up on YES", time: "18:07" },
	],
	"4": [
		{ id: "m1", user: "CryptoCat", text: "BTC going to the moon! 🚀", time: "21:30" },
		{ id: "m2", user: "BearHunter", text: "Don’t get rekt guys, set your stops.", time: "21:31" },
		{ id: "m3", user: "SatoshiFan", text: "Who’s long from 65k?", time: "21:32" },
	],
	"5": [
		{ id: "m1", user: "WaveRider", text: "Love the vibe here in Athens!", time: "10:05" },
		{ id: "m2", user: "OceanDev", text: "Collab ideas flowing already.", time: "10:06" },
		{ id: "m3", user: "BuildSquad", text: "Retreat energy is 🔥", time: "10:07" },
	],
	default: [
		{ id: "m1", user: "Viewer1", text: "Hello everyone!", time: "12:00" },
		{ id: "m2", user: "Viewer2", text: "Great stream so far", time: "12:01" },
		{ id: "m3", user: "Viewer3", text: "😄", time: "12:02" },
	],
};

const ChatView: React.FC<Props> = ({ gameId }) => {
	const initMessages = mockChats[gameId ?? ""] ?? mockChats.default;
	const { messages, addMessage, seedMessages } = useChatMessages();
	const [input, setInput] = useState("");

	// Seed initial messages when component mounts or gameId changes
	useEffect(() => {
		seedMessages(initMessages, gameId);
	}, [gameId, seedMessages, initMessages]);

	const handleSend = () => {
		if (!input.trim()) return;
		const now = new Date();
		const newMsg: ChatMessage = {
			id: `n${Date.now()}`,
			user: "You",
			text: input.trim(),
			time: now.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
		};
		addMessage(newMsg); // Use shared state instead of local state
		setInput("");
	};

	return (
		<div className="rounded-lg border border-gray-200 bg-white flex flex-col h-[320px]">
			{/* Header */}
			<div className="px-3 py-2 border-b border-gray-200 bg-gray-50">
				<h3 className="text-sm font-semibold">Live Chat</h3>
			</div>

			{/* Messages */}
			<div className="flex-1 overflow-y-auto px-3 py-2 space-y-1 text-sm max-h-[200px]">
				{messages.map(msg => (
					<div key={msg.id} className="flex items-start gap-2">
						<span className="font-semibold text-blue-600">{msg.user}</span>
						<span className="text-gray-700 flex-1">{msg.text}</span>
						<span className="text-xs text-gray-400">{msg.time}</span>
					</div>
				))}
			</div>

			{/* Input box */}
			<div className="px-3 py-2 border-t border-gray-200 bg-gray-50">
				<div className="flex items-center gap-2">
					<input
						type="text"
						className="flex-1 rounded border border-gray-300 px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
						placeholder="Type a message..."
						value={input}
						onChange={e => setInput(e.target.value)}
						onKeyDown={e => {
							if (e.key === "Enter") {
								e.preventDefault();
								handleSend();
							}
						}}
					/>
					<button
						className="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium px-3 py-1 rounded"
						onClick={handleSend}
					>
						Send
					</button>
				</div>
			</div>
		</div>
	);
};

export default ChatView;
