import React, { createContext, useContext, useState, ReactNode } from "react";

export interface ChatMessage {
	id: string;
	user: string;
	text: string;
	time: string;
}

interface ChatMessagesContextValue {
	messages: ChatMessage[];
	addMessage: (msg: ChatMessage) => void;
	seedMessages: (msgs: ChatMessage[], gameId?: string) => void;
}

const ChatMessagesContext = createContext<ChatMessagesContextValue | undefined>(undefined);

export const ChatMessagesProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
	const [messages, setMessages] = useState<ChatMessage[]>([]);
	const [currentGameId, setCurrentGameId] = useState<string>("");

	const addMessage = (msg: ChatMessage) => {
		setMessages(prev => [...prev, msg]);
	};

	// allow seeding with game-specific messages
	const seedMessages = (msgs: ChatMessage[], gameId?: string) => {
		// If switching to a different game, reset messages
		if (gameId && gameId !== currentGameId) {
			setMessages(msgs);
			setCurrentGameId(gameId);
		} else if (messages.length === 0) {
			// Only seed if no messages exist
			setMessages(msgs);
			if (gameId) setCurrentGameId(gameId);
		}
	};

	return (
		<ChatMessagesContext.Provider value={{ messages, addMessage, seedMessages }}>
			{children}
		</ChatMessagesContext.Provider>
	);
};

export const useChatMessages = () => {
	const ctx = useContext(ChatMessagesContext);
	if (!ctx) throw new Error("useChatMessages must be used within ChatMessagesProvider");
	return ctx;
};
