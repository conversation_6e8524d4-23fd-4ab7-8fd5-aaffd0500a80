# 弹幕系统 (Danmaku System)

## 功能概述

这个弹幕系统为直播页面的视频播放器添加了实时弹幕效果，支持将聊天消息以滚动文字的形式显示在视频上方。

## 主要特性

### ✅ 已实现功能

1. **实时弹幕显示**
   - 用户在聊天框发送的新消息会立即显示为弹幕
   - 弹幕从右向左平滑滚动
   - 支持多条消息同时显示

2. **多轨道系统**
   - 6条水平轨道避免消息重叠
   - 智能轨道分配算法
   - 轨道冷却机制防止拥挤

3. **动画效果**
   - 3种滚动速度：快速、正常、慢速
   - 新用户消息优先使用快速或正常速度
   - CSS动画确保流畅性能

4. **用户控制**
   - 弹幕开关按钮（🎭 ON/OFF）
   - 实时切换显示/隐藏

5. **游戏特定内容**
   - 根据gameId显示不同的初始消息
   - 支持游戏切换时的消息重置

## 技术实现

### 核心组件

1. **useDanmaku Hook** (`useDanmaku.ts`)
   - 管理弹幕状态和动画
   - 处理轨道分配和冷却
   - 检测新消息并立即显示

2. **ChatMessagesProvider** (`chat-context.tsx`)
   - 全局聊天状态管理
   - 连接聊天组件和弹幕系统
   - 支持游戏切换时的消息同步

3. **LivePlayView** (`index.tsx`)
   - 视频播放器组件
   - 弹幕覆盖层渲染
   - 用户控制界面

### 数据流

```
用户发送消息 → ChatView → ChatMessagesProvider → LivePlayView → useDanmaku → 弹幕显示
```

## 使用方法

### 基本使用

1. 访问 `/live` 页面
2. 在右侧聊天框输入消息并发送
3. 消息会立即在视频上方显示为弹幕
4. 点击 🎭 按钮可以开关弹幕显示

### 游戏切换

- 使用URL参数切换游戏：`/live?id=1`, `/live?id=2` 等
- 每个游戏有不同的初始弹幕消息集合
- 切换游戏时会重置聊天和弹幕状态

## 配置选项

在 `useDanmaku.ts` 中可以调整以下参数：

```typescript
const DANMAKU_LANES = 6;        // 弹幕轨道数量
const MESSAGE_INTERVAL = 2000;  // 循环消息间隔(毫秒)
const LANE_COOLDOWN = 3000;     // 轨道冷却时间(毫秒)
```

## 样式定制

弹幕样式在 `src/styles/common/animate.css` 中定义：

```css
.danmaku-message {
  animation: danmaku-scroll 8s linear;
}

.danmaku-fast {
  animation-duration: 6s;
}

.danmaku-slow {
  animation-duration: 10s;
}
```

## 性能优化

1. **内存管理**：动画完成后自动清理弹幕消息
2. **高效渲染**：只渲染当前活跃的弹幕
3. **CSS动画**：使用硬件加速的CSS变换
4. **轨道管理**：防止过度拥挤影响性能

## 测试

项目包含测试组件 `danmaku-test.tsx` 用于调试和验证功能。

## 未来扩展

- WebSocket实时聊天集成
- 用户偏好设置保存
- 消息过滤和审核
- 自定义样式主题
- 点击交互功能
