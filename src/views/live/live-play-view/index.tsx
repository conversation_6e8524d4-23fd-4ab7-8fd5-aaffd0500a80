// <!-- live-play -->
import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import ReactPlayer from "react-player";
import { useDanmaku } from "./useDanmaku";
import { useChatMessages } from "../chat-context";

// Mock chat messages keyed by game ID (same structure as chat-view)
const mockChats: Record<string, Array<{ id: string; user: string; text: string; time: string }>> = {
	"1": [
		{ id: "m1", user: "GGWP", text: "That clutch play was insane!", time: "16:44" },
		{ id: "m2", user: "NoScope", text: "MVP right there", time: "16:45" },
		{ id: "m3", user: "ProStrat", text: "Next round eco?", time: "16:46" },
	],
	"2": [
		{ id: "m1", user: "StatsGeek", text: "That was a crazy 3-pointer!", time: "20:10" },
		{ id: "m2", user: "DataDunk", text: "Model says 78% win prob now.", time: "20:11" },
		{ id: "m3", user: "CourtSide", text: "Let's goooo 🔥", time: "20:12" },
	],
	"3": [
		{ id: "m1", user: "MarketGuru", text: "New poll results just dropped", time: "18:05" },
		{ id: "m2", user: "TrendSpot", text: "Odds shifting fast!", time: "18:06" },
		{ id: "m3", user: "AlphaWolf", text: "Loading up on YES", time: "18:07" },
	],
	"4": [
		{ id: "m1", user: "CryptoCat", text: "BTC going to the moon! 🚀", time: "21:30" },
		{ id: "m2", user: "BearHunter", text: "Don't get rekt guys, set your stops.", time: "21:31" },
		{ id: "m3", user: "SatoshiFan", text: "Who's long from 65k?", time: "21:32" },
	],
	default: [
		{ id: "m1", user: "GameMaster", text: "This match is incredible!", time: "22:00" },
		{ id: "m2", user: "PredictPro", text: "Called it! Easy money 💰", time: "22:01" },
		{ id: "m3", user: "StreamFan", text: "Best stream ever!", time: "22:02" },
		{ id: "m4", user: "Viewer1", text: "Hello everyone!", time: "12:00" },
		{ id: "m5", user: "Viewer2", text: "Great stream so far", time: "12:01" },
		{ id: "m6", user: "Viewer3", text: "😄", time: "12:02" },
		{ id: "m7", user: "TechGuru", text: "Amazing graphics quality!", time: "12:03" },
		{ id: "m8", user: "SportsFan", text: "What a play!", time: "12:04" },
		{ id: "m9", user: "Trader123", text: "Time to buy!", time: "12:05" },
		{ id: "m10", user: "Analyst", text: "Market looking bullish", time: "12:06" },
	],
};

interface Props {
	gameId?: string;
}

const View: React.FC<Props> = ({ gameId }) => {
	const location = useLocation();
	const params = new URLSearchParams(location.search);
	const liveStreamUrl = params.get("url") || "https://www.youtube.com/watch?v=6Sz2bGhqt2s"; // fallback

	// Danmaku toggle state
	const [isDanmakuEnabled, setIsDanmakuEnabled] = useState(true);

	// Use shared chat messages from context
	const { messages: sharedMessages, seedMessages } = useChatMessages();

	// Seed initial messages for the current game
	const initMessages = mockChats[gameId ?? ""] ?? mockChats.default;
	useEffect(() => {
		seedMessages(initMessages, gameId);
	}, [gameId, seedMessages, initMessages]);

	// Use shared messages for danmaku (includes both initial and new user messages)
	const chatMessages = sharedMessages.length > 0 ? sharedMessages : initMessages;

	// Initialize danmaku system (only when enabled)
	const { activeDanmaku, DANMAKU_LANES } = useDanmaku(isDanmakuEnabled ? chatMessages : []);

	return (
		<div className="w-full">
			<div className="w-full border-2 border-text-200 rounded-xl overflow-hidden relative">
				<ReactPlayer
					url={liveStreamUrl}
					width="100%"
					height="auto"
					controls
					playing
					muted
					config={{
						youtube: {
							playerVars: {
								autoplay: 1,
							},
						},
					}}
					className="aspect-video"
				/>

				{/* Danmaku Toggle Button */}
				<button
					onClick={() => setIsDanmakuEnabled(!isDanmakuEnabled)}
					className="absolute top-4 right-4 z-20 bg-black/60 hover:bg-black/80 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors backdrop-blur-sm"
					title={isDanmakuEnabled ? "Hide Danmaku" : "Show Danmaku"}
				>
					{isDanmakuEnabled ? "🎭 ON" : "🎭 OFF"}
				</button>

				{/* Danmaku Overlay */}
				{isDanmakuEnabled && (
					<div className="absolute inset-0 pointer-events-none overflow-hidden rounded-xl">
						{/* Create lanes for danmaku messages */}
						{Array.from({ length: DANMAKU_LANES }, (_, laneIndex) => (
							<div
								key={laneIndex}
								className="absolute w-full"
								style={{
									top: `${(laneIndex + 1) * (100 / (DANMAKU_LANES + 2))}%`,
									height: `${100 / (DANMAKU_LANES + 2)}%`,
								}}
							>
								{/* Render danmaku messages for this lane */}
								{activeDanmaku
									.filter(msg => msg.lane === laneIndex)
									.map(msg => (
										<div
											key={msg.id}
											className={`absolute whitespace-nowrap danmaku-message ${
												msg.speed === "fast"
													? "danmaku-fast"
													: msg.speed === "slow"
														? "danmaku-slow"
														: ""
											}`}
											style={{
												top: "50%",
												transform: "translateY(-50%)",
												zIndex: 10,
											}}
										>
											<span className="inline-block px-2 py-1 bg-black/60 text-white text-sm font-medium rounded-md shadow-lg backdrop-blur-sm">
												<span className="text-blue-300 font-semibold">{msg.user}:</span>{" "}
												<span>{msg.text}</span>
											</span>
										</div>
									))}
							</div>
						))}
					</div>
				)}
			</div>
		</div>
	);
};

export default View;
