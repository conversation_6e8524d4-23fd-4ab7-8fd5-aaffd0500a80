import { useState, useEffect, useCallback } from "react";

export interface DanmakuMessage {
	id: string;
	text: string;
	user: string;
	lane: number;
	speed: "fast" | "normal" | "slow";
}

interface ChatMessage {
	id: string;
	user: string;
	text: string;
	time: string;
}

const DANMAKU_LANES = 6; // Number of horizontal lanes for danmaku
const MESSAGE_INTERVAL = 2000; // Interval between new messages (ms)
const LANE_COOLDOWN = 3000; // Cooldown time for each lane (ms)

export const useDanmaku = (chatMessages: ChatMessage[]) => {
	const [activeDanmaku, setActiveDanmaku] = useState<DanmakuMessage[]>([]);
	const [messageIndex, setMessageIndex] = useState(0);
	const [laneCooldowns, setLaneCooldowns] = useState<number[]>(new Array(DANMAKU_LANES).fill(0));
	const [lastMessageCount, setLastMessageCount] = useState(0);

	// Get available lane (not in cooldown)
	const getAvailableLane = useCallback(() => {
		const now = Date.now();
		for (let i = 0; i < DANMAKU_LANES; i++) {
			if (laneCooldowns[i] <= now) {
				return i;
			}
		}
		// If all lanes are busy, use the one with shortest cooldown
		let minCooldownIndex = 0;
		let minCooldown = laneCooldowns[0];
		for (let i = 1; i < DANMAKU_LANES; i++) {
			if (laneCooldowns[i] < minCooldown) {
				minCooldown = laneCooldowns[i];
				minCooldownIndex = i;
			}
		}
		return minCooldownIndex;
	}, [laneCooldowns]);

	// Create danmaku message from chat message
	const createDanmakuMessage = useCallback(
		(chatMessage: ChatMessage, isNewMessage = false) => {
			const lane = getAvailableLane();
			const speeds = ["fast", "normal", "slow"] as const;
			// New messages get priority speed (fast or normal)
			const speed = isNewMessage
				? Math.random() > 0.5
					? "fast"
					: "normal"
				: speeds[Math.floor(Math.random() * speeds.length)];

			const danmakuMessage: DanmakuMessage = {
				id: `danmaku-${Date.now()}-${Math.random()}`,
				text: chatMessage.text,
				user: chatMessage.user,
				lane,
				speed,
			};

			setActiveDanmaku(prev => [...prev, danmakuMessage]);

			// Set lane cooldown
			const now = Date.now();
			setLaneCooldowns(prev => {
				const newCooldowns = [...prev];
				newCooldowns[lane] = now + LANE_COOLDOWN;
				return newCooldowns;
			});

			// Remove message after animation completes
			setTimeout(() => {
				setActiveDanmaku(prev => prev.filter(msg => msg.id !== danmakuMessage.id));
			}, 12000); // Max animation duration + buffer
		},
		[getAvailableLane],
	);

	// Add new danmaku message from cycling through existing messages
	const addDanmakuMessage = useCallback(() => {
		if (chatMessages.length === 0) return;

		const chatMessage = chatMessages[messageIndex % chatMessages.length];
		createDanmakuMessage(chatMessage, false);

		// Move to next message
		setMessageIndex(prev => prev + 1);
	}, [chatMessages, messageIndex, createDanmakuMessage]);

	// Start danmaku system
	useEffect(() => {
		if (chatMessages.length === 0) return;

		const interval = setInterval(addDanmakuMessage, MESSAGE_INTERVAL);

		return () => clearInterval(interval);
	}, [addDanmakuMessage, chatMessages.length]);

	// Detect new messages and immediately show them as danmaku
	useEffect(() => {
		if (chatMessages.length > lastMessageCount && lastMessageCount > 0) {
			// New message(s) added, show the latest one immediately
			const newMessage = chatMessages[chatMessages.length - 1];
			createDanmakuMessage(newMessage, true);
		}
		setLastMessageCount(chatMessages.length);
	}, [chatMessages, lastMessageCount, createDanmakuMessage]);

	// Clear danmaku when chat messages change dramatically (like switching games)
	useEffect(() => {
		if (chatMessages.length === 0) {
			setActiveDanmaku([]);
			setMessageIndex(0);
			setLaneCooldowns(new Array(DANMAKU_LANES).fill(0));
			setLastMessageCount(0);
		}
	}, [chatMessages.length]);

	return {
		activeDanmaku,
		DANMAKU_LANES,
	};
};
