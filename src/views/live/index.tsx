// <!-- game -->
import React, { useState, useRef } from "react";
import { useLocation } from "react-router-dom";

// 导入原 home 页面的组件
import TransactionView from "./transaction-view/index";
import LivePlayView from "./live-play-view/index";
import PickingView from "./picking-view/index";
import ChatView from "./chat-view/index";
import ForumView from "./forum-view/index";
import { ChatMessagesProvider } from "./chat-context";

// 导入优雅的错误状态组件
import {
	GentleNotification,
	VideoPlayerFallback,
	ChatFallback,
	GenericFallback,
} from "@/components/ui/elegant-error-states";
import { ShimmerStyles } from "@/components/ui/skeleton-loaders";

// Error Boundary Component
class ErrorBoundary extends React.Component<
	{ children: React.ReactNode; fallback?: React.ReactNode },
	{ hasError: boolean; error?: Error }
> {
	constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError(error: Error) {
		return { hasError: true, error };
	}

	componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
		console.error("Error caught by boundary:", error, errorInfo);
	}

	render() {
		if (this.state.hasError) {
			return this.props.fallback || <GenericFallback title="Something went wrong" />;
		}

		return this.props.children;
	}
}

const View: React.FC = () => {
	const [isLoading, setIsLoading] = useState<boolean>(false); //loading
	const [hasError, setHasError] = useState<boolean>(false); // error state
	const [errorMessage, setErrorMessage] = useState<string>(""); // error message

	const changIsLoading = (state: boolean) => {
		setIsLoading(state);
	};

	// Handle errors from child components
	const handleError = (error: string) => {
		setHasError(true);
		setErrorMessage(error);
		console.error("Live page error:", error);
	};

	// Clear error state
	const clearError = () => {
		setHasError(false);
		setErrorMessage("");
	};

	// 选中的交易
	const [pickingId, setPickingId] = useState<any>();
	// 选中交易事件
	const changePickingId = (item: any) => {
		setPickingId(item);
	};

	// 创建一个引用，用于调用 PickingView 中的 onContract 方法
	const pickingViewRef = useRef<{
		onContract: () => Promise<void>;
	}>(null);

	// 创建一个刷新市场数据的函数，供 TransactionView 调用
	const refreshMarketData = () => {
		// 调用 PickingView 中的 onContract 方法刷新数据
		if (pickingViewRef.current) {
			console.log("调用 PickingView 中的 onContract 方法刷新数据");
			pickingViewRef.current.onContract();
		}
	};

	const location = useLocation();
	const params = new URLSearchParams(location.search);
	const gameIdParam = params.get("id") || undefined;

	return (
		<ChatMessagesProvider>
			<ShimmerStyles />
			<section className="main py-4 px-4">
				{/* Gentle error notification */}
				{hasError && (
					<GentleNotification
						title="Game Not Found"
						message={
							errorMessage || "The requested game could not be loaded. Showing default content."
						}
						onDismiss={clearError}
						type="info"
					/>
				)}

				{/* Main container for desktop layout rows and mobile layout */}
				<div className="flex flex-col gap-4">
					{/* First Row: LivePlayView and Desktop TransactionView */}
					<div className="flex justify-between items-stretch gap-10">
						{/* Left side column: Live video and prediction list */}
						<div className="flex-1 flex flex-col gap-4">
							<ErrorBoundary fallback={<VideoPlayerFallback />}>
								<LivePlayView gameId={gameIdParam} />
							</ErrorBoundary>

							{/* Prediction list and forum directly under video */}
							<div className="w-full flex flex-col gap-4">
								<ErrorBoundary
									fallback={<GenericFallback title="Prediction markets temporarily unavailable" />}
								>
									<PickingView
										ref={pickingViewRef}
										pickingId={pickingId}
										changePickingId={changePickingId}
										isLoading={isLoading}
										changIsLoading={changIsLoading}
										gameId={gameIdParam}
										onError={handleError}
									/>
								</ErrorBoundary>

								<ErrorBoundary fallback={<GenericFallback title="Forum temporarily unavailable" />}>
									<ForumView gameId={gameIdParam} />
								</ErrorBoundary>
							</div>
						</div>

						{/* Right side stack: Chat and Transaction */}
						<div className="hidden lg:flex w-[340px] flex-col gap-4">
							{/* Chat window always visible */}
							<ErrorBoundary fallback={<ChatFallback />}>
								<ChatView gameId={gameIdParam} />
							</ErrorBoundary>

							{/* Transaction view conditional */}
							{isLoading && (
								<ErrorBoundary
									fallback={<GenericFallback title="Trading temporarily unavailable" />}
								>
									<TransactionView pickingId={pickingId} refreshMarketData={refreshMarketData} />
								</ErrorBoundary>
							)}
						</div>
					</div>
				</div>

				{/* Mobile: Chat and Transaction stacked */}
				<div className="lg:hidden w-full mt-4 flex flex-col gap-4">
					<ErrorBoundary fallback={<ChatFallback />}>
						<ChatView gameId={gameIdParam} />
					</ErrorBoundary>

					{isLoading && (
						<ErrorBoundary fallback={<GenericFallback title="Trading temporarily unavailable" />}>
							<TransactionView pickingId={pickingId} refreshMarketData={refreshMarketData} />
						</ErrorBoundary>
					)}
				</div>
			</section>
		</ChatMessagesProvider>
	);
};

export default View;
