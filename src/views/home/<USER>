// <!-- home -->
import React from "react";
import { useNavigate } from "react-router-dom";
import { useStreams, LiveStreamData } from "@/context/stream-context";

// Utility to extract YouTube video id from a URL
const extractYoutubeId = (url: string): string | null => {
	const regex =
		/(?:youtube\.com\/(?:[^/\n\s]+\/\S+\/|(?:v|e(?:mbed)?|shorts)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
	const match = url.match(regex);
	return match ? match[1] : null;
};

// Get thumbnail URL (prefer explicit thumbnail, else derive from YouTube, else placeholder)
const getThumbnail = (stream: LiveStreamData): string => {
	if (stream.thumbnail) return stream.thumbnail;
	if (stream.youtubeUrl) {
		const id = extractYoutubeId(stream.youtubeUrl);
		if (id) return `https://img.youtube.com/vi/${id}/hqdefault.jpg`;
	}
	return "https://via.placeholder.com/640x360/E5E7EB/6B7280?text=Video+Thumbnail";
};

const View: React.FC = () => {
	const navigate = useNavigate();
	const { streams } = useStreams();

	// Handle card click to navigate to game page
	const handleCardClick = (stream: LiveStreamData) => {
		// Navigate to /live with the YouTube url in query string so the game page can load the specific video
		navigate(`/live?url=${encodeURIComponent(stream.youtubeUrl)}&id=${stream.id}`);
	};

	// Get status badge color
	const getStatusBadgeColor = (status: string) => {
		switch (status) {
			case "live":
				return "bg-red-500 text-white";
			case "upcoming":
				return "bg-yellow-500 text-white";
			case "ended":
				return "bg-gray-500 text-white";
			default:
				return "bg-gray-500 text-white";
		}
	};

	return (
		<section className="main py-10">
			<div className="mb-8">
				<h1 className="text-3xl font-bold mb-6">Live Prediction Streams</h1>
				<p className="text-gray-600 mb-8">
					Watch live streams and participate in real-time prediction markets
				</p>
			</div>

			{/* Live stream cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 gap-6">
				{streams.map(stream => (
					<div
						key={stream.id}
						className="rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-105"
						onClick={() => handleCardClick(stream)}
					>
						{/* Thumbnail */}
						<div className="relative bg-gray-100 pb-[56.25%]">
							<img
								src={getThumbnail(stream)}
								alt={stream.title}
								className="absolute inset-0 w-full h-full object-cover bg-gray-200"
								crossOrigin="anonymous"
								loading="lazy"
								onError={e => {
									const img = e.target as HTMLImageElement;
									// Fallback to placeholder on any error
									img.src =
										"https://via.placeholder.com/640x360/E5E7EB/6B7280?text=Video+Thumbnail";
								}}
							/>
							{/* Status badge */}
							<div className="absolute top-3 left-3">
								<span
									className={`px-2 py-1 rounded-full text-xs font-semibold uppercase ${getStatusBadgeColor(stream.status)}`}
								>
									{stream.status}
								</span>
							</div>
							{/* Viewers count */}
							<div className="absolute top-3 right-3">
								<span className="bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
									👁 {stream.viewers}
								</span>
							</div>
							{/* Play button overlay */}
							<div className="absolute inset-0 flex items-center justify-center bg-black/0 hover:bg-black/30 transition-all duration-300">
								<div className="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
									<svg
										className="w-6 h-6 text-gray-800 ml-1"
										fill="currentColor"
										viewBox="0 0 20 20"
									>
										<path d="M8 5v10l8-5-8-5z" />
									</svg>
								</div>
							</div>
						</div>

						{/* Content */}
						<div className="p-4">
							<h3 className="text-lg font-semibold mb-2 text-gray-900">{stream.title}</h3>
							<p className="text-gray-600 text-sm mb-4 line-clamp-3">{stream.description}</p>

							{/* Action button */}
							<button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors duration-200 font-medium">
								Join Stream & Predict
							</button>
						</div>
					</div>
				))}
			</div>
		</section>
	);
};

export default View;
