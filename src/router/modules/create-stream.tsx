// create-stream 路由
import React from "react";
import lazyLoad from "@/router/utils/lazy-load";
import Layout from "@/layout/index";
import { RouteObject } from "@/router/interface";

const CreateStreamRouter: Array<RouteObject> = [
	{
		element: <Layout />, // 导航
		children: [
			{
				path: "/create",
				element: lazyLoad(React.lazy(() => import("@/views/create-stream/index"))),
				meta: {
					title: "create-stream-index",
					isRoot: false,
				},
			},
		],
	},
];

export default CreateStreamRouter;
